# SignatureCoordinates API Endpoints

## Overview
API endpoints để quản lý vị tr<PERSON> chữ ký (SignatureCoordinate) trong hệ thống GoTRUST EMR.

## Endpoints

### GET /medical-record-templates/{templateId}/signature-coordinates

L<PERSON>y danh sách vị trí chữ ký cho một template bệnh án dựa trên role của user hiện tại.

#### Parameters
- `templateId` (Guid, required): ID của Medical Record Template

#### Authentication
- Requires: JWT Bearer token
- Authorization: User phải đăng nhập và có role hợp lệ

#### Response
```json
{
  "data": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "medicalRecordTemplateId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "roleId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "name": "Ch<PERSON> ký bác sĩ",
      "coordinateX": 100.5,
      "coordinateY": 200.75,
      "pageNumber": 1,
      "width": 150.0,
      "height": 50.0,
      "allowAutoSign": true,
      "roleName": "Bác sĩ",
      "templateName": "Phiếu khám bệnh"
    }
  ],
  "isSuccess": true,
  "message": null
}
```

#### Business Logic
1. Lấy thông tin user hiện tại từ JWT token
2. Lấy danh sách roles của user
3. Query SignatureCoordinates theo:
   - `MedicalRecordTemplateId` = templateId từ route
   - `RoleId` thuộc danh sách roles của user hiện tại
4. Sắp xếp kết quả theo: PageNumber → CoordinateY → CoordinateX
5. Trả về danh sách với thông tin đầy đủ bao gồm RoleName và TemplateName

#### Error Responses
- `401 Unauthorized`: User chưa đăng nhập hoặc token không hợp lệ
- `404 Not Found`: Không tìm thấy template hoặc user không có quyền
- `400 Bad Request`: templateId không hợp lệ

#### Example Usage

```bash
# Get signature coordinates for template
curl -X GET \
  "https://api.gotrust.vn/emr-management/medical-record-templates/123e4567-e89b-12d3-a456-426614174000/signature-coordinates" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## Implementation Details

### Files Structure
```
Services/GoTRUST.EMR.API/Endpoints/SignatureCoordinates/
├── GetSignatureCoordinatesEndpoint.cs    # Carter endpoint module
└── README.md                             # This documentation

Services/GoTRUST.EMR.Application/Features/SignatureCoordinates/Queries/
└── GetSignatureCoordinatesQuery.cs       # CQRS Query & Handler
```

### Dependencies
- **Carter**: Minimal API routing
- **MediatR**: CQRS pattern implementation
- **Entity Framework Core**: Database access
- **ASP.NET Core Identity**: User authentication & role management

### Security Considerations
- Endpoint requires authentication via JWT Bearer token
- User chỉ có thể xem signature coordinates cho roles mà họ được assign
- Không có authorization check cho specific template (business requirement)
- Logging được implement để track access patterns

### Performance Notes
- Query sử dụng `AsNoTracking()` để optimize read performance
- Include navigation properties (Role, MedicalRecordTemplate) để giảm N+1 queries
- Kết quả được sắp xếp để đảm bảo consistent ordering
