using System.ComponentModel.DataAnnotations.Schema;
using GoTRUST.EMR.Domain.Abstractions;

namespace GoTRUST.EMR.Domain.Models
{
    /// <summary>
    /// Junction table for managing signature coordinates between MedicalRecordTemplate and Role
    /// </summary>
    public class SignatureCoordinate : Entity<Guid>
    {
        /// <summary>
        /// Name of the signature coordinate
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Medical record template ID
        /// </summary>
        public Guid MedicalRecordTemplateId { get; set; }

        /// <summary>
        /// Role ID
        /// </summary>
        public Guid RoleId { get; set; }

        /// <summary>
        /// X coordinate for signature position
        /// </summary>
        public decimal CoordinateX { get; set; }

        /// <summary>
        /// Y coordinate for signature position
        /// </summary>
        public decimal CoordinateY { get; set; }

        /// <summary>
        /// Page number for signature position
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Width of the signature area
        /// </summary>
        public decimal Width { get; set; }

        /// <summary>
        /// Height of the signature area
        /// </summary>
        public decimal Height { get; set; }

        /// <summary>
        /// Allow automatic signing for this role on this template
        /// </summary>
        public bool AllowAutoSign { get; set; } = false;

        /// <summary>
        /// Navigation property to MedicalRecordTemplate
        /// </summary>
        public virtual MedicalRecordTemplate MedicalRecordTemplate { get; set; } = null!;

        /// <summary>
        /// Navigation property to Role
        /// </summary>
        public virtual Role Role { get; set; } = null!;
    }
}
