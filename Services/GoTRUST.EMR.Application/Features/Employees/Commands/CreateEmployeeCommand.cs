﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Exceptions;
using Microsoft.AspNetCore.Identity;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using GoTRUST.EMR.Domain.Constants;
using BuildingBlocks.Common.Interface;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Employees.Commands;

public class CreateEmployeeRequest : ICommand<Response<CreateEmployeeResponse>>
{
    public string FullName { get; init; } = string.Empty;
    public string EmployeeCode { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;
    public Guid RoleId { get; init; }
    public IFormFile? Avatar { get; init; }
}

public record CreateEmployeeResponse(
    bool IsSuccess,
    Guid EmployeeId,
    Guid UserId
);

public class CreateEmployeeRequestValidator : AbstractValidator<CreateEmployeeRequest>
{
    public CreateEmployeeRequestValidator()
    {
        RuleFor(x => x.FullName).NotEmpty().MaximumLength(255);
        RuleFor(x => x.Email).NotEmpty().MaximumLength(255).EmailAddress();
        RuleFor(x => x.EmployeeCode).NotEmpty().MaximumLength(100);
        RuleFor(x => x.RoleId).NotEmpty();
    }
}

public class CreateEmployeeHandler(
    IApplicationDbContext _dbContext,
    UserManager<User> _userManager,
    RoleManager<Role> _roleManager,
    IConfiguration _config,
    IWebHostEnvironment _env,
    IHttpContextAccessor _httpContextAccessor,
    IFileStorageService _fileStorageService
) : ICommandHandler<CreateEmployeeRequest, Response<CreateEmployeeResponse>>
{
    public async Task<Response<CreateEmployeeResponse>> Handle(CreateEmployeeRequest req, CancellationToken cancellationToken)
    {
        var currentUserId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var currentUser = await _userManager.FindByIdAsync(currentUserId.ToString()) ?? throw new UserNotFoundException();
        var hospitalId = currentUser.HospitalId;

        if (await _dbContext.Employees.AnyAsync(e => e.EmployeeCode == req.EmployeeCode && e.HospitalId == hospitalId, cancellationToken))
            throw new EmployeeCodeAlreadyExistsException(req.EmployeeCode);

        var existingUser = await _userManager.FindByEmailAsync(req.Email);
        if (existingUser != null)
            throw new BadRequestException("Email đã tồn tại");

        var user = new User
        {
            UserName = req.Email,
            Email = req.Email,
            FullName = req.FullName,
            HospitalId = hospitalId,
            UserType = UserTypeConstants.Internal
        };

        var tempPassword = StringExtensions.GenerateRandomPassword();
        var createUserResult = await _userManager.CreateAsync(user, tempPassword);

        if (!createUserResult.Succeeded)
            throw new BadRequestException("Tạo tài khoản thất bại: " + createUserResult.Errors.FirstOrDefault()!.Description);

        if (req.Avatar != null && req.Avatar.Length > 0)
        {
            using var ms = new MemoryStream();
            await req.Avatar.CopyToAsync(ms, cancellationToken);
            ms.Position = 0;
            var fileKey = $"avatars/{user.Id}{Path.GetExtension(req.Avatar.FileName)}";
            var (success, message, fileUrl) = await _fileStorageService.UploadFileAsync(ms, fileKey);
            if (!success)
                throw new BadRequestException($"Không thể upload ảnh đại diện: {message}");
            user.AvatarUrl = fileUrl;
        }
        var role = await _roleManager.FindByIdAsync(req.RoleId.ToString()) ?? throw new NotFoundException($"Không tìm thấy quyền với Id: {req.RoleId}");

        var addRoleResult = await _userManager.AddToRoleAsync(user, role.Name!);
        if (!addRoleResult.Succeeded)
            throw new BadRequestException("Không thể thêm quyền cho tài khoản");

        var employee = new Employee
        {
            FullName = req.FullName,
            Email = req.Email,
            EmployeeCode = req.EmployeeCode,
            UserId = user.Id,
            HospitalId = (Guid)hospitalId!
        };
        _dbContext.Employees.Add(employee);

        var expiresAt = DateTime.UtcNow.AddMinutes(int.TryParse(_config["UpdatePasswordExpireInMinutes"], out var minutes) ? minutes : 10080);

        var session = new ForgotPasswordSession
        {
            UserId = user.Id,
            ExpiresAt = expiresAt
        };

        _dbContext.ForgotPasswordSessions.Add(session);


        var resetLink = $"{_config["ClientUrl"]}/tao-mat-khau?session={session.Id}&user={user.UserName}";

        var templatePath = Path.Combine(_env.WebRootPath, "Templates", "Mails", "EMRForgotPassword.html");
        var template = await File.ReadAllTextAsync(templatePath, cancellationToken);
        template = template.Replace("{{AccountName}}", user.FullName ?? user.UserName);
        template = template.Replace("{{CreatePassword}}", resetLink);

        _ = MailHelper.SendAsync(
            int.TryParse(_config["EmailPort"], out int port) ? port : 587,
            _config["EmailHost"]!,
            _config["EmailPassword"]!,
            _config["EmailFromMail"]!,
            user.Email,
            _config["EmailDisplayName"]!,
            "Tài khoản nhân viên mới",
            template
        );

        employee.AddDomainEvent(new EmployeeCreateEvent(employee));
        
        if (await _dbContext.SaveChangesAsync(cancellationToken) <= 0)
            throw new SaveFailedException("Employee");

        return new Response<CreateEmployeeResponse>(
            new CreateEmployeeResponse(
                true,
                employee.Id,
                user.Id
            )
        );
    }
}