﻿using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Identity;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using GoTRUST.EMR.Domain.Events;

namespace GoTRUST.EMR.Application.Features.Authenticates.Commands;

public record ForgotPasswordRequest(string Account) : ICommand<Response<ForgotPasswordResponse>>;

public record ForgotPasswordResponse(
    string Account,
    string ExpiresAt
);

public class ForgotPasswordRequestValidator : AbstractValidator<ForgotPasswordRequest>
{
    public ForgotPasswordRequestValidator()
    {
    }
}

public class ForgotPasswordHandler(
    IApplicationDbContext _unitOfWork,
    IConfiguration _config,
    UserManager<User> userManager,
    IWebHostEnvironment env)
    : ICommandHandler<ForgotPasswordRequest, Response<ForgotPasswordResponse>>
{
    private readonly UserManager<User> _userManager = userManager;
    private readonly IWebHostEnvironment _env = env;
    public async Task<Response<ForgotPasswordResponse>> Handle(ForgotPasswordRequest request, CancellationToken cancellationToken)
    {
        if (!request.Account.IsEmail())
        {
            throw new BadRequestException("Tài khoản không hợp lệ");
        }

        var foundUser = await _userManager.FindByUserNameOrEmailOrPhoneAsync(request.Account) ?? throw new NotFoundException("Tài khoản không tồn tại");
        foundUser.AddDomainEvent(new ForgotPasswordEvent(foundUser));
        var expiresAt = DateTime.UtcNow.AddMinutes(int.TryParse(_config["UpdatePasswordExpireInMinutes"], out var minutes) ? minutes : 5);

        var session = new ForgotPasswordSession
        {
            UserId = foundUser.Id,
            ExpiresAt = expiresAt
        };

        _unitOfWork.ForgotPasswordSessions.Add(session);

        if (await _unitOfWork.SaveChangesAsync(cancellationToken) <= 0)
        {
            throw new BadRequestException("Lưu dữ liệu thất bại");
        }

        //generate reset link
        var resetLink = $"{_config["ClientUrl"]}/tao-mat-khau?session={session.Id}&user={foundUser.UserName}";

        // get email template path from wwwroot path
        var templatePath = Path.Combine(_env.WebRootPath, "Templates", "Mails", "EMRForgotPassword.html");
        var template = await File.ReadAllTextAsync(templatePath, cancellationToken);

        // replace placeholders in template
        template = template.Replace("{{AccountName}}", foundUser.FullName ?? foundUser.UserName);
        template = template.Replace("{{CreatePassword}}", resetLink);

        //send email
        _ = MailHelper.SendAsync(
            int.TryParse(_config["EmailPort"], out int port) ? port : 587,
            _config["EmailHost"]!,
            _config["EmailPassword"]!,
            _config["EmailFromMail"]!,
            request.Account,
            _config["EmailDisplayName"]!,
            _config["UpdatePasswordEmailSubject"]!,
            template
        );

        return new Response<ForgotPasswordResponse>(new ForgotPasswordResponse(
            request.Account,
            expiresAt.ToString("O")
        ));
    }
}