﻿using BuildingBlocks.Abstractions;
using BuildingBlocks.Common.Interface;
using BuildingBlocks.Exceptions;
using GoTRUST.EMR.Application.Features.Upload.Commands;
using GoTRUST.EMR.Application.Helpers;
using GoTRUST.EMR.Domain.Enums;
using Microsoft.AspNetCore.Identity;

namespace GoTRUST.EMR.Application.Features.Signatures.Commands;

public class SignMedicalRecordCommand : ICommand<Response<SignMedicalRecordResponse>>
{
    public string Serial { get; init; } = string.Empty;
    public SignPosition Position { get; init; } = null!;
    public Guid MedicalRecordFileId { get; init; }
}

public record SignPosition(int X, int Y, int Width, int Height, int Page);

public record SignMedicalRecordResponse(Guid MedicalRecordFileId);

public class SignMedicalRecordCommandValidator : AbstractValidator<SignMedicalRecordCommand>
{
    public SignMedicalRecordCommandValidator()
    {
        RuleFor(x => x.Serial).NotEmpty();
        RuleFor(x => x.MedicalRecordFileId).NotEmpty();
        RuleFor(x => x.Position).NotNull();
    }
}
public class SignMedicalRecordCommandHandler(
    IApplicationDbContext _dbContext,
    ISignService _signService,
    IHttpClientFactory _httpClientFactory,
    ISender _mediator,
    IHttpContextAccessor _httpContextAccessor,
    UserManager<User> _userManager
) : ICommandHandler<SignMedicalRecordCommand, Response<SignMedicalRecordResponse>>
{
    public async Task<Response<SignMedicalRecordResponse>> Handle(SignMedicalRecordCommand req, CancellationToken cancellationToken)
    {
        var userId = _httpContextAccessor.HttpContext?.User.RetrieveUserIdFromPrincipal();
        var user = await _userManager.FindByIdAsync(userId.ToString() ?? string.Empty) ?? throw new UserNotFoundException();

        // Find PIN number for the given serial
        var signInfo = user.SignSerials?.FirstOrDefault(s => s.SignSerial == req.Serial);
        if (signInfo == null || string.IsNullOrEmpty(signInfo.SignPinNumber))
            throw new BadRequestException($"Không tìm thấy PIN number cho serial: {req.Serial}");

        var pinNumber = signInfo.SignPinNumber;

        var medicalRecordFile = await _dbContext.MedicalRecordFiles
            .FindAsync([req.MedicalRecordFileId], cancellationToken)
            ?? throw new NotFoundException($"Không tìm thấy MedicalRecordFile với Id: {req.MedicalRecordFileId}");

        if (string.IsNullOrEmpty(medicalRecordFile.FilePath))
            throw new NotFoundException($"Không tìm thấy file PDF cho MedicalRecordId: {req.MedicalRecordFileId}");

        using var httpClient = _httpClientFactory.CreateClient();
        var response = await httpClient.GetAsync(medicalRecordFile.FilePath, cancellationToken);
        response.EnsureSuccessStatusCode();

        byte[] pdfBytes = await response.Content.ReadAsByteArrayAsync(cancellationToken);

        // Create IFormFile from downloaded bytes
        var stream = new MemoryStream(pdfBytes);
        var formFile = new FormFile(stream, 0, pdfBytes.Length, "file", medicalRecordFile.FileName)
        {
            Headers = new HeaderDictionary(),
            ContentType = medicalRecordFile.FileType
        };

        // Decrypt the file using MediatR
        var decryptCommand = new DecryptFileCommand
        {
            File = formFile,
            IV = medicalRecordFile.InitialVector
        };
        var decryptResult = await _mediator.Send(decryptCommand, cancellationToken);

        if (decryptResult.Code != "000" || string.IsNullOrEmpty(decryptResult.Data))
            throw new BadRequestException("Failed to decrypt PDF file");

        // Read the decrypted PDF file
        var decryptedBytes = await File.ReadAllBytesAsync(decryptResult.Data, cancellationToken);
        var base64Pdf = Convert.ToBase64String(decryptedBytes);

        var signResult = await _signService.SignVisiblePdfAsync(
            base64Pdf,
            medicalRecordFile.FileName,
            req.Serial,
            pinNumber,
            req.Position.X,
            req.Position.Y,
            req.Position.Width,
            req.Position.Height,
            req.Position.Page
        );

        if (signResult.Status != 0)
            throw new Exception("Lỗi khi ký file PDF: " + signResult.Msg);

        byte[] signedPdfBytes = Convert.FromBase64String(signResult.Data!);
        var signedStream = new MemoryStream(signedPdfBytes);
        var signedFormFile = new FormFile(signedStream, 0, signedPdfBytes.Length, "file", medicalRecordFile.FileName)
        {
            Headers = new HeaderDictionary(),
            ContentType = medicalRecordFile.FileType
        };

        // Encrypt the signed PDF using MediatR
        var encryptCommand = new EncryptFileCommand(signedFormFile);
        var encryptResult = await _mediator.Send(encryptCommand, cancellationToken);

        if (encryptResult.Code != "000" || encryptResult.Data == null)
            throw new BadRequestException("Failed to encrypt signed PDF file");

        // Clean up temporary decrypted file
        if (File.Exists(decryptResult.Data))
            File.Delete(decryptResult.Data);

        medicalRecordFile.FilePath = encryptResult.Data.FileUrl;
        medicalRecordFile.InitialVector = encryptResult.Data.IV;
        _dbContext.MedicalRecordFiles.Update(medicalRecordFile);

        var medicalRecord = await _dbContext.MedicalRecords
            .FindAsync([medicalRecordFile.MedicalRecordId], cancellationToken);

        if (medicalRecord != null)
        {
            medicalRecord.Status = MedicalRecordStatus.Draft;
            _dbContext.MedicalRecords.Update(medicalRecord);
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        return new Response<SignMedicalRecordResponse>(new SignMedicalRecordResponse(medicalRecordFile.Id));
    }
}